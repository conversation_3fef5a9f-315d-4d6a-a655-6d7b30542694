import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/router/router.dart';

class NoticeCarouselView extends StatefulWidget {
  final List<dynamic> noticeData;
  final Function(Map<String, dynamic>)? onNoticeTap;

  const NoticeCarouselView({
    super.key,
    required this.noticeData,
    this.onNoticeTap,
  });

  @override
  State<NoticeCarouselView> createState() => _NoticeCarouselViewState();
}

class _NoticeCarouselViewState extends State<NoticeCarouselView> with WidgetsBindingObserver, RouteAware {
  int _currentNoticeIndex = 0;
  Timer? _noticeTimer;
  bool _isAppInForeground = true;
  bool _isRouteActive = true;
  bool _isVisible = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // 延迟启动轮播，确保页面已经渲染完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && widget.noticeData.isNotEmpty) {
        // 注册路由监听
        final route = ModalRoute.of(context);
        if (route is PageRoute) {
          RouterHelper.routeObserver.subscribe(this, route);
        }
        // 初始化可见性检测
        _checkVisibility();
      }
    });
  }

  @override
  void didUpdateWidget(NoticeCarouselView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当数据发生变化时，重新启动轮播
    if (widget.noticeData != oldWidget.noticeData) {
      _currentNoticeIndex = 0;
      _checkAndStartCarousel();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    RouterHelper.routeObserver.unsubscribe(this);
    _noticeTimer?.cancel();
    super.dispose();
  }

  // RouteAware 方法 - 监听路由变化
  @override
  void didPush() {
    logger.i('NoticeCarousel didPush - 页面进入');
    _isRouteActive = true;
    _checkAndStartCarousel();
  }

  @override
  void didPopNext() {
    logger.i('NoticeCarousel didPopNext - 从其他页面返回');
    _isRouteActive = true;
    _checkAndStartCarousel();
  }

  @override
  void didPushNext() {
    logger.i('NoticeCarousel didPushNext - 跳转到其他页面');
    _isRouteActive = false;
    _pauseCarousel();
  }

  @override
  void didPop() {
    logger.i('NoticeCarousel didPop - 页面退出');
    _isRouteActive = false;
    _pauseCarousel();
  }

  // 检查是否应该开始轮播
  void _checkAndStartCarousel() {
    if (_isVisible && _isAppInForeground && _isRouteActive) {
      _startNoticeCarousel();
    }
  }

  // 监听应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logger.i('NoticeCarousel 应用回到前台');
        _isAppInForeground = true;
        _checkAndStartCarousel();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        logger.i('NoticeCarousel 应用进入后台');
        _isAppInForeground = false;
        _pauseCarousel();
        break;
    }
  }

  // 暂停轮播
  void _pauseCarousel() {
    logger.i('_pauseCarousel');
    _noticeTimer?.cancel();
    _noticeTimer = null;
  }

  // 开始轮播
  void _startNoticeCarousel() {
    if (widget.noticeData.isEmpty || !_isAppInForeground || !_isRouteActive || !_isVisible || !mounted) return;

    _noticeTimer?.cancel();
    _noticeTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (_isAppInForeground && _isRouteActive && _isVisible && mounted) {
        _nextNotice();
      } else {
        _pauseCarousel();
      }
    });
  }

  // 切换到下一个通知
  void _nextNotice() {
    if (widget.noticeData.isEmpty || !mounted) return;

    logger.i('_nextNotice');
    setState(() {
      _currentNoticeIndex = (_currentNoticeIndex + 1) % widget.noticeData.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.noticeData.isEmpty) return const SizedBox.shrink();

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        // 检测滚动时的可见性
        _checkVisibility();
        return false;
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
        color: const Color(0xFFF5F5F5),
        child: Column(
          children: [
            Row(
              children: [
                const Text(
                  '通知',
                  style: TextStyle(
                    fontSize: 17,
                    height: 1,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 17,
            ),
            // 轮播容器
            Container(
              height: 44,
              padding: const EdgeInsets.symmetric(horizontal: 10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 150),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 1),
                      end: Offset.zero,
                    ).animate(animation),
                    child: child,
                  );
                },
                child: GestureDetector(
                  key: ValueKey(_currentNoticeIndex),
                  onTap: () {
                    if (widget.onNoticeTap != null) {
                      widget.onNoticeTap!(widget.noticeData[_currentNoticeIndex]);
                    }
                  },
                  child: Container(
                    height: 44,
                    child: Row(
                      children: [
                        Icon(Icons.campaign, size: 18, color: MColor.skin),
                        const SizedBox(width: 10),
                        Text(
                          widget.noticeData[_currentNoticeIndex]['title'],
                          style: TextStyle(
                            height: 1.4,
                            fontSize: 14,
                            color: MColor.xFF1B1C1A,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  // 检测组件可见性
  void _checkVisibility() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      try {
        final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
        if (renderBox == null) return;

        final Offset position = renderBox.localToGlobal(Offset.zero);
        final Size size = renderBox.size;
        final Size screenSize = MediaQuery.of(context).size;

        // 检查组件是否在屏幕可见区域内
        final bool isVisible =
            position.dy < screenSize.height && position.dy + size.height > 0 && position.dx < screenSize.width && position.dx + size.width > 0;

        if (_isVisible != isVisible) {
          logger.i('NoticeCarousel 可见性变化: $_isVisible -> $isVisible');
          _isVisible = isVisible;
          if (isVisible) {
            _checkAndStartCarousel();
          } else {
            _pauseCarousel();
          }
        }
      } catch (e) {
        logger.e('检测可见性时出错: $e');
      }
    });
  }
}
