import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';

class NoticeCarouselView extends StatefulWidget {
  final List<dynamic> noticeData;
  final Function(Map<String, dynamic>)? onNoticeTap;

  const NoticeCarouselView({
    super.key,
    required this.noticeData,
    this.onNoticeTap,
  });

  @override
  State<NoticeCarouselView> createState() => _NoticeCarouselViewState();
}

class _NoticeCarouselViewState extends State<NoticeCarouselView> {
  int _currentNoticeIndex = 0;
  Timer? _noticeTimer;

  @override
  void initState() {
    super.initState();
    _startNoticeCarousel();
  }

  @override
  void didUpdateWidget(NoticeCarouselView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当数据发生变化时，重新启动轮播
    if (widget.noticeData != oldWidget.noticeData) {
      _currentNoticeIndex = 0;
      _startNoticeCarousel();
    }
  }

  @override
  void dispose() {
    _noticeTimer?.cancel();
    super.dispose();
  }

  // 开始轮播
  void _startNoticeCarousel() {
    if (widget.noticeData.isEmpty) return;

    _noticeTimer?.cancel();
    _noticeTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _nextNotice();
    });
  }

  // 切换到下一个通知
  void _nextNotice() {
    if (widget.noticeData.isEmpty) return;

    setState(() {
      _currentNoticeIndex = (_currentNoticeIndex + 1) % widget.noticeData.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.noticeData.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
      color: const Color(0xFFF5F5F5),
      child: Column(
        children: [
          Row(
            children: [
              const Text(
                '通知',
                style: TextStyle(
                  fontSize: 17,
                  height: 1,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 17,
          ),
          // 轮播容器
          Container(
            height: 44,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 150),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 1),
                    end: Offset.zero,
                  ).animate(animation),
                  child: child,
                );
              },
              child: GestureDetector(
                key: ValueKey(_currentNoticeIndex),
                onTap: () {
                  if (widget.onNoticeTap != null) {
                    widget.onNoticeTap!(widget.noticeData[_currentNoticeIndex]);
                  }
                },
                child: Container(
                  height: 44,
                  child: Row(
                    children: [
                      Icon(Icons.campaign, size: 18, color: MColor.skin),
                      const SizedBox(width: 10),
                      Text(
                        widget.noticeData[_currentNoticeIndex]['title'],
                        style: TextStyle(
                          height: 1.4,
                          fontSize: 14,
                          color: MColor.xFF1B1C1A,
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
