import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_add_page.dart';
import 'package:qiazhun/modules/account/account_common_widgets.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class AccountAddSavingSection extends StatefulWidget {
  final AccountAddController? addController;
  final AccountModel? accountModel;
  const AccountAddSavingSection({this.addController, this.accountModel, super.key});

  @override
  State<StatefulWidget> createState() => _AccountAddSavingState();
}

class _AccountAddSavingState extends State<AccountAddSavingSection> with AutomaticKeepAliveClientMixin {
  bool _inAssets = true;

  final TextEditingController _balanceController = TextEditingController();
  final TextEditingController _cardNoController = TextEditingController();

  final List<BankItem> _bankList = [];

  BankItem? _selectedBankItem;

  @override
  void initState() {
    widget.addController?.onAction = _addCard;
    _initData();
    if (widget.accountModel != null) {
      _cardNoController.text = widget.accountModel!.accountName ?? '';
      _balanceController.text = widget.accountModel!.balance ?? '';
      _inAssets = widget.accountModel!.isJoinTotal == 1;
      _selectedBankItem =
          BankItem(widget.accountModel!.bankId, widget.accountModel!.bankName, widget.accountModel!.bankIcon, null, widget.accountModel!.hiddenType);
    }
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AccountAddSavingSection oldWidget) {
    widget.addController?.onAction = _addCard;
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _balanceController.dispose();
    _cardNoController.dispose();
    super.dispose();
  }

  Future<void> _initData() async {
    Loading.show();
    try {
      var resp = await AccountRepo.getBankList();
      if (resp.code == 1) {
        _bankList.clear();
        _bankList.addAll(resp.data ?? []);
      }
    } catch (e) {
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _bankSection,
          const SizedBox(
            height: 14,
          ),
          _detailSection,
          const SizedBox(
            height: 14,
          ),
        ],
      ),
    );
  }

  Future<void> _addCard() async {
    if (_balanceController.text.isEmpty) {
      showToast('请填写余额');
      return;
    }
    if (_cardNoController.text.isEmpty) {
      showToast('请填写卡号');
      return;
    }
    if (_selectedBankItem == null) {
      showToast('请先选择机构类型');
      return;
    }

    Loading.show();
    try {
      var resp = await AccountRepo.addSavingCard(
        id: widget.accountModel?.id,
        accountType: '1',
        balance: _balanceController.text,
        isJoinTotal: _inAssets ? '1' : '2',
        bankId: '${_selectedBankItem!.id}',
        cardNo: _cardNoController.text,
      );
      if (resp.code == 1) {
        showToast('添加账号成功');
        RouterHelper.router.pop();
      } else {
        showToast(resp.msg ?? '添加账号失败');
      }
    } catch (e) {
      showToast('添加账号失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  Widget get _detailSection {
    return Container(
      // margin: EdgeInsets.symmetric(horizontal: 14),
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 0),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
      child: Column(
        children: [
          AccountInputTile(
            leadingText: '账户名称',
            hint: '6个字符以内，如建白金',
            maxLength: 6,
            textController: _cardNoController,
          ),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          AccountInputTile(
            leadingText: '余额',
            textController: _balanceController,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()],
          ),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          AccountSwitchTile(
            leadingText: '是否计入资产',
            initValue: _inAssets,
            onChanged: (v) {
              setState(() {
                _inAssets = v;
              });
            },
          )
        ],
      ),
    );
  }

  Widget get _bankSection {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        RouterHelper.router.pushNamed(Routes.bankListPath, extra: {'onlyBank': true}).then((value) {
          if (value is BankItem) {
            _selectedBankItem = value;
            setState(() {});
          }
        });
      },
      child: Container(
        // margin: EdgeInsets.symmetric(horizontal: 14),
        padding: EdgeInsets.symmetric(horizontal: 14, vertical: 12),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
        child: Row(
          children: [
            if (_selectedBankItem == null) ...{
              Text(
                '请选择机构类型',
                style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
              )
            } else ...{
              RoundImage(imageUrl: getImageUrl(_selectedBankItem!.bankImage ?? ''), radius: 16, size: 32),
              const SizedBox(
                width: 10,
              ),
              Text(
                _selectedBankItem!.bankName ?? '',
                style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
              )
            }
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
